//
//  FundRecommendationStateTests.swift
//  HomeTests
//
//  Created by Augment Agent on 25/08/2025.
//

import XCTest

@testable import Home

class FundRecommendationStateTests: XCTestCase {

    // MARK: - Test Data

    private func createMockFundRecommendationCategory(childrenCount: Int = 3)
        -> FundRecommendationCategory
    {
        let children = (0..<childrenCount).map { index in
            FundRecommendationChild(
                categoryId: index,
                categoryName: "Category \(index)",
                instruments: []
            )
        }

        return FundRecommendationCategory(
            categoryId: 1,
            categoryName: "Test Category",
            children: children,
            instruments: []
        )
    }

    // MARK: - Tests

    func testCategoryIndexValidation() {
        let category = createMockFundRecommendationCategory(childrenCount: 3)

        // Test valid index
        let validIndex = 1
        let adjustedValidIndex = min(validIndex, max(0, category.children.count - 1))
        XCTAssertEqual(adjustedValidIndex, 1, "Valid index should remain unchanged")

        // Test index too high
        let highIndex = 5
        let adjustedHighIndex = min(highIndex, max(0, category.children.count - 1))
        XCTAssertEqual(adjustedHighIndex, 2, "High index should be clamped to max valid index")

        // Test negative index
        let negativeIndex = -1
        let adjustedNegativeIndex = min(negativeIndex, max(0, category.children.count - 1))
        XCTAssertEqual(adjustedNegativeIndex, -1, "Negative index should be handled by max(0, ...)")
        let finalIndex = max(0, adjustedNegativeIndex)
        XCTAssertEqual(finalIndex, 0, "Final index should be 0 for negative input")
    }

    func testEmptyChildrenHandling() {
        let category = createMockFundRecommendationCategory(childrenCount: 0)

        let selectedIndex = 1
        let adjustedIndex = min(selectedIndex, max(0, category.children.count - 1))
        XCTAssertEqual(adjustedIndex, 0, "Index should be 0 when no children exist")
    }

    func testStatePreservationLogic() {
        // Simulate the state preservation logic from HomeViewController
        var selectedFundCategoryIndex = 2
        let category = createMockFundRecommendationCategory(childrenCount: 3)

        // Validate index (simulating validateSelectedFundCategoryIndex method)
        let maxIndex = max(0, category.children.count - 1)
        selectedFundCategoryIndex = min(selectedFundCategoryIndex, maxIndex)

        XCTAssertEqual(selectedFundCategoryIndex, 2, "Valid index should be preserved")

        // Test with smaller category
        let smallerCategory = createMockFundRecommendationCategory(childrenCount: 2)
        let smallerMaxIndex = max(0, smallerCategory.children.count - 1)
        selectedFundCategoryIndex = min(selectedFundCategoryIndex, smallerMaxIndex)

        XCTAssertEqual(
            selectedFundCategoryIndex, 1, "Index should be adjusted to fit smaller category")
    }

    func testCellConfigurationWithPreservedIndex() {
        let category = createMockFundRecommendationCategory(childrenCount: 3)

        // Test default behavior (index 0)
        let defaultSelectedIndex = 0
        let configuredIndex1 = min(defaultSelectedIndex, max(0, category.children.count - 1))
        XCTAssertEqual(configuredIndex1, 0, "Default index should be 0")

        // Test preserved index
        let preservedIndex = 2
        let configuredIndex2 = min(preservedIndex, max(0, category.children.count - 1))
        XCTAssertEqual(configuredIndex2, 2, "Preserved index should be maintained")

        // Test out-of-bounds preserved index
        let outOfBoundsIndex = 5
        let configuredIndex3 = min(outOfBoundsIndex, max(0, category.children.count - 1))
        XCTAssertEqual(configuredIndex3, 2, "Out-of-bounds index should be clamped")
    }
}
