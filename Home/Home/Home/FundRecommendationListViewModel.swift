//
//  FundRecommendationListViewModel.swift
//  Home
//
//  Created by Augment Agent on 25/08/2025.
//

import Core
import Foundation
import RxCocoa
import RxSwift
import SharedData
import XCoordinator

final class FundRecommendationListViewModel: AnyViewModel {

    // MARK: Properties
    private let router: UnownedRouter<HomeRoute>
    private let category: FundRecommendationChild
    private let mainCategoryName: String
    private let disposeBag = DisposeBag()

    // MARK: Input/Output
    struct Input {
        let onViewAppear: Observable<Void>
    }

    struct Output {
        let title: Driver<String>
        let instruments: Driver<[FundRecommendationInstrument]>
    }

    // MARK: Initialization
    init(
        router: UnownedRouter<HomeRoute>, category: FundRecommendationChild,
        mainCategoryName: String
    ) {
        self.router = router
        self.category = category
        self.mainCategoryName = mainCategoryName
    }

    // MARK: Transform
    func transform(input: Input) -> Output {
        // Create title combining main category and selected category
        let title = Observable.just("\(mainCategoryName) - \(category.categoryName)")

        // Provide all instruments from the selected category
        let instruments = Observable.just(category.instruments)

        return Output(
            title: title.asDriverOnErrorNever(),
            instruments: instruments.asDriverOnErrorNever()
        )
    }

    // MARK: - Navigation

    func navigateToInstrumentDetail(_ fundInstrument: FundRecommendationInstrument) {
        let marketInstrument = convertToMarketInstrument(from: fundInstrument)
        router.trigger(.instrumentDetail(instrument: marketInstrument))
    }

    // MARK: - Utility Methods

    /// Converts FundRecommendationInstrument to MarketInstrument for navigation
    private func convertToMarketInstrument(from fundInstrument: FundRecommendationInstrument)
        -> MarketInstrument
    {
        return MarketInstrument(
            id: fundInstrument.instrumentId,
            symbol: fundInstrument.symbol,
            exchange: fundInstrument.exchange.isEmpty ? nil : fundInstrument.exchange,
            TimeZone: nil,
            market: nil,
            instrumentName: fundInstrument.instrumentName,
            currency: fundInstrument.currency.isEmpty ? nil : fundInstrument.currency,
            logo: fundInstrument.logo,
            riskLevel: fundInstrument.riskLevel,
            riskRating: nil,
            instrumentClass: nil,
            instrumentType: nil,
            instrumentCategory: nil,
            allocation: fundInstrument.allocationClass,
            assetClass: nil,
            lastPrice: fundInstrument.price,
            priceChange: fundInstrument.priceChange,
            priceChangePercentage: fundInstrument.priceChangeRate,
            favorite: nil,
            totalVolume: nil,
            totalAmount: nil
        )
    }
}
