//
//  FundRecommendationListViewController.swift
//  Home
//
//  Created by Augment Agent on 25/08/2025.
//

import CUIModule
import Core
import RxCocoa
import RxSwift
import SharedData
import UIKit

final class FundRecommendationListViewController: BaseViewController, VMView {

    // MARK: UI Properties
    private lazy var navView: CUINavigationBar = {
        let view = CUINavigationBar(
            displayModel: CUINavigationBar.DisplayModel(
                leftIcon: .image(named: "merit_ic_back"),
                titleView: titleLabel
            )
        )
        return view
    }()

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 14),
        textColor: Color.txtTitle
    )

    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = Color.bgDefault
        collectionView.showsVerticalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self

        // Register cells
        collectionView.register(
            FundItemCollectionViewCell.self,
            forCellWithReuseIdentifier: FundItemCollectionViewCell.reuseIdentifier)

        return collectionView
    }()

    // MARK: Properties
    public var viewModel: FundRecommendationListViewModel!
    private var instruments: [FundRecommendationInstrument] = []

    // MARK: Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindViewModel()
    }

    // MARK: Setup
    public func setupUI() {
        view.backgroundColor = Color.bgDefault
        navigationBarHidden(true)

        view.addSubviews([navView, collectionView])

        navView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.height.equalTo(44)
        }

        collectionView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }

    private func bindViewModel() {
        // Handle back navigation directly in the view controller
        navView.rx.leftTap
            .subscribe(onNext: { [weak self] in
                self?.navigationController?.popViewController(animated: true)
            })
            .disposed(by: disposeBag)

        let input = FundRecommendationListViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid()
        )

        let output = viewModel.transform(input: input)

        output.title
            .drive(titleLabel.rx.text)
            .disposed(by: disposeBag)

        output.instruments
            .drive(onNext: { [weak self] instruments in
                self?.instruments = instruments
                self?.collectionView.reloadData()
            })
            .disposed(by: disposeBag)
    }

    public func bind(viewModel: FundRecommendationListViewModel) {
        self.viewModel = viewModel
    }
}

// MARK: - UICollectionViewDataSource
extension FundRecommendationListViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
        -> Int
    {
        return instruments.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath)
        -> UICollectionViewCell
    {
        let cell =
            collectionView.dequeueReusableCell(
                withReuseIdentifier: FundItemCollectionViewCell.reuseIdentifier,
                for: indexPath) as! FundItemCollectionViewCell
        let instrument = instruments[indexPath.item]
        cell.configure(with: instrument)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension FundRecommendationListViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)

        guard indexPath.item < instruments.count else { return }
        let instrument = instruments[indexPath.item]

        viewModel.navigateToInstrumentDetail(instrument)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension FundRecommendationListViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(
        _ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout,
        sizeForItemAt indexPath: IndexPath
    ) -> CGSize {
        let width = collectionView.frame.width - 32  // Account for section insets
        return CGSize(width: width, height: 80)  // Same height as FundItemView
    }
}

// MARK: - FundItemCollectionViewCell
private class FundItemCollectionViewCell: UICollectionViewCell, AnyView {

    private lazy var containerView = {
        let view = UIView()
        view.backgroundColor = Color.bgDefault
        view.layer.cornerRadius = 8
        return view
    }()

    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical, spacing: 8)
        stackView.addArrangedSubviews([topRowStackView, bottomRowStackView])
        return stackView
    }()

    private lazy var topRowStackView = {
        let stackView = UIStackView(axis: .horizontal)
        stackView.addArrangedSubviews([instrumentNameLabel, UIView(), priceChangeRateLabel])
        return stackView
    }()

    private lazy var bottomRowStackView = {
        let stackView = UIStackView(axis: .horizontal)
        stackView.addArrangedSubviews([leftBottomStackView, UIView(), periodLabel])
        return stackView
    }()

    private lazy var leftBottomStackView = {
        let stackView = UIStackView(axis: .horizontal, spacing: 8)
        stackView.addArrangedSubviews([allocationClassLabel, riskLevelBadge])
        return stackView
    }()

    private lazy var instrumentNameLabel = UILabel(
        font: Font.bold.of(size: 14), textColor: Color.txtTitle)
    private lazy var priceChangeRateLabel = UILabel(
        font: Font.semiBold.of(size: 14), textAlignment: .right)
    private lazy var allocationClassLabel = UILabel(
        font: Font.regular.of(size: 12), textColor: Color.txtParagraph)
    private lazy var riskLevelBadge = RiskLevelBadge()
    private lazy var periodLabel = UILabel(
        font: Font.regular.of(size: 12), textColor: Color.txtParagraph, textAlignment: .right)

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    public func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(contentStackView)

        containerView.snp.makeConstraints {
            $0.edges.equalToSuperview()
        }

        contentStackView.snp.makeConstraints {
            $0.edges.equalToSuperview().inset(12)
        }
    }

    func configure(with instrument: FundRecommendationInstrument) {
        instrumentNameLabel.text = instrument.instrumentName

        // Configure price change rate with color and format to 2 decimal places
        if let priceChangeRate = instrument.priceChangeRate,
            let rateValue = Double(priceChangeRate)
        {
            let formattedRate = String(format: "%.2f", rateValue)
            let percentage = "\(formattedRate)%"

            if rateValue < 0 {
                priceChangeRateLabel.text = percentage
                priceChangeRateLabel.textColor = Color.txtNegative
            } else {
                priceChangeRateLabel.text = "+\(percentage)"
                priceChangeRateLabel.textColor = Color.txtPositive
            }
        } else {
            priceChangeRateLabel.text = "0.00%"
            priceChangeRateLabel.textColor = Color.txtParagraph
        }

        // Configure allocation class with proper capitalization (first letter uppercase)
        if let allocationClass = instrument.allocationClass {
            allocationClassLabel.text = allocationClass.capitalized
        } else {
            allocationClassLabel.text = ""
        }

        // Configure risk level with standard app styling
        riskLevelBadge.updateRiskLevel(RiskLevel(rawValue: instrument.riskLevel ?? ""))

        periodLabel.text = instrument.period ?? ""
    }
}
