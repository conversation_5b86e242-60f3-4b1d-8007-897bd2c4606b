//
//  FundRecommendationCell.swift
//  Home
//
//  Created by Augment Agent on 24/08/2025.
//

import UIKit
import CUIModule
import RxSwift
import RxCocoa
import SharedData

final class FundRecommendationCell: UICollectionViewCell {
    
    // MARK: - UI Properties
    private lazy var containerStackView = {
        let stackView = UIStackView(axis: .vertical, spacing: 16)
        stackView.addArrangedSubviews([categoryButtonsStackView, fundsStackView])
        return stackView
    }()
    
    private lazy var categoryButtonsStackView = {
        let stackView = UIStackView(axis: .horizontal, distribution: .fillEqually, spacing: 8)
        return stackView
    }()
    
    private lazy var fundsStackView = {
        let stackView = UIStackView(axis: .vertical, spacing: 12)
        return stackView
    }()
    
    // MARK: - Properties
    private var categoryButtons: [UIButton] = []
    private var fundViews: [FundItemView] = []
    private var productCategory: FundRecommendationCategory?
    private var selectedCategoryIndex = 0
    
    var disposeBag = DisposeBag()
    
    // MARK: - Reactive Properties
    private let categorySelectionSubject = PublishSubject<Int>()
    var categorySelection: Observable<Int> {
        return categorySelectionSubject.asObservable()
    }
    
    // MARK: - Lifecycle
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        disposeBag = DisposeBag()
        clearContent()
    }
    
    // MARK: - Setup
    func setupUI() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        contentView.addSubview(containerStackView)
        
        containerStackView.snp.makeConstraints {
            $0.edges.equalToSuperview().inset(16)
        }
    }
    
    private func clearContent() {
        categoryButtons.forEach { $0.removeFromSuperview() }
        categoryButtons.removeAll()
        
        fundViews.forEach { $0.removeFromSuperview() }
        fundViews.removeAll()
        
        categoryButtonsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        fundsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
    }
    
    // MARK: - Configuration
    func configure(with productCategory: FundRecommendationCategory, selectedCategoryIndex: Int = 0) {
        self.productCategory = productCategory
        self.selectedCategoryIndex = min(selectedCategoryIndex, max(0, productCategory.children.count - 1))

        setupCategoryButtons()
        updateFundsList()
    }
    
    private func setupCategoryButtons() {
        guard let productCategory = productCategory else { return }
        
        clearContent()
        
        // Create buttons for first 3 children categories
        let categories = Array(productCategory.children.prefix(3))
        
        for (index, category) in categories.enumerated() {
            let button = createCategoryButton(title: category.categoryName, index: index)
            categoryButtons.append(button)
            categoryButtonsStackView.addArrangedSubview(button)
        }
        
        // Select the specified button (or first button by default)
        if !categoryButtons.isEmpty {
            selectCategoryButton(at: selectedCategoryIndex)
        }
    }
    
    private func createCategoryButton(title: String, index: Int) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = Font.medium.of(size: 14)
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 1
        button.contentEdgeInsets = UIEdgeInsets(top: 8, left: 12, bottom: 8, right: 12)
        
        // Set initial appearance
        updateButtonAppearance(button, isSelected: index == selectedCategoryIndex)
        
        button.rx.tap
            .subscribe(onNext: { [weak self] in
                self?.selectCategoryButton(at: index)
                self?.categorySelectionSubject.onNext(index)
            })
            .disposed(by: disposeBag)
        
        return button
    }
    
    private func selectCategoryButton(at index: Int) {
        selectedCategoryIndex = index
        
        // Update button appearances
        for (buttonIndex, button) in categoryButtons.enumerated() {
            updateButtonAppearance(button, isSelected: buttonIndex == index)
        }
        
        updateFundsList()
    }
    
    private func updateButtonAppearance(_ button: UIButton, isSelected: Bool) {
        if isSelected {
            button.backgroundColor = Color.btnPrimary
            button.setTitleColor(.white, for: .normal)
            button.layer.borderColor = Color.btnPrimary.cgColor
        } else {
            button.backgroundColor = .clear
            button.setTitleColor(Color.txtTitle, for: .normal)
            button.layer.borderColor = Color.txtParagraph.cgColor
        }
    }
    
    private func updateFundsList() {
        guard let productCategory = productCategory,
              selectedCategoryIndex < productCategory.children.count else { return }
        
        // Clear existing fund views
        fundViews.forEach { $0.removeFromSuperview() }
        fundViews.removeAll()
        fundsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // Get instruments for selected category
        let selectedCategory = productCategory.children[selectedCategoryIndex]
        let instruments = Array(selectedCategory.instruments.prefix(3))
        
        // Create fund item views
        for instrument in instruments {
            let fundView = FundItemView()
            fundView.configure(with: instrument)
            fundViews.append(fundView)
            fundsStackView.addArrangedSubview(fundView)
        }
    }
}

// MARK: - FundItemView
private class FundItemView: UIView {
    
    private lazy var containerView = {
        let view = UIView()
        view.backgroundColor = Color.bgDefault
        view.layer.cornerRadius = 8
        return view
    }()
    
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical, spacing: 8)
        stackView.addArrangedSubviews([topRowStackView, bottomRowStackView])
        return stackView
    }()
    
    private lazy var topRowStackView = {
        let stackView = UIStackView(axis: .horizontal)
        stackView.addArrangedSubviews([instrumentNameLabel, UIView(), priceChangeRateLabel])
        return stackView
    }()
    
    private lazy var bottomRowStackView = {
        let stackView = UIStackView(axis: .horizontal)
        stackView.addArrangedSubviews([leftBottomStackView, UIView(), periodLabel])
        return stackView
    }()
    
    private lazy var leftBottomStackView = {
        let stackView = UIStackView(axis: .horizontal, spacing: 8)
        stackView.addArrangedSubviews([allocationClassLabel, riskLevelBadge])
        return stackView
    }()
    
    private lazy var instrumentNameLabel = UILabel(font: Font.bold.of(size: 14), textColor: Color.txtTitle)
    private lazy var priceChangeRateLabel = UILabel(font: Font.semiBold.of(size: 14), textAlignment: .right)
    private lazy var allocationClassLabel = UILabel(font: Font.regular.of(size: 12), textColor: Color.txtParagraph)
    private lazy var riskLevelBadge = RiskLevelBadge()
    private lazy var periodLabel = UILabel(font: Font.regular.of(size: 12), textColor: Color.txtParagraph, textAlignment: .right)
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(containerView)
        containerView.addSubview(contentStackView)

        containerView.snp.makeConstraints {
            $0.edges.equalToSuperview()
        }

        contentStackView.snp.makeConstraints {
            $0.edges.equalToSuperview().inset(12)
        }
    }
    
    func configure(with instrument: FundRecommendationInstrument) {
        instrumentNameLabel.text = instrument.instrumentName

        // Configure price change rate with color and format to 2 decimal places
        if let priceChangeRate = instrument.priceChangeRate,
           let rateValue = Double(priceChangeRate) {
            let formattedRate = String(format: "%.2f", rateValue)
            let percentage = "\(formattedRate)%"

            if rateValue < 0 {
                priceChangeRateLabel.text = percentage
                priceChangeRateLabel.textColor = Color.txtNegative
            } else {
                priceChangeRateLabel.text = "+\(percentage)"
                priceChangeRateLabel.textColor = Color.txtPositive
            }
        } else {
            priceChangeRateLabel.text = "0.00%"
            priceChangeRateLabel.textColor = Color.txtParagraph
        }

        // Configure allocation class with proper capitalization (first letter uppercase)
        if let allocationClass = instrument.allocationClass {
            allocationClassLabel.text = allocationClass.capitalized
        } else {
            allocationClassLabel.text = ""
        }

        // Configure risk level with standard app styling
        riskLevelBadge.updateRiskLevel(RiskLevel(rawValue: instrument.riskLevel ?? ""))

        periodLabel.text = instrument.period ?? ""
    }
}
