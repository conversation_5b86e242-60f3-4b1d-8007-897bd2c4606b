//
//  HomeViewController.swift
//  Home
//
//  Created by <PERSON> on 19/02/2024.
//

import CUIModule
import Core
import RxCocoa
import RxSwift
import SafariServices
import SharedData
import Storage
import UIKit

// swiftlint:disable line_length
final class HomeViewController: BaseViewController, VMView {

    // MARK: UI properties
    private lazy var searchBar = CUISearchBar(
        placeholder: "key0400".localized(),
        showBackButton: false)

    private lazy var refreshControl = {
        let control = UIRefreshControl()
        control.tintColor = Color.bgButtonPositive

        return control
    }()

    lazy var collectionView = {
        let collectionView = UICollectionView(
            layout: collectionViewLayout(),
            backgroundColor: .clear,
            contentInset: UIEdgeInsets(bottom: 12))

        collectionView.register(WalletSummaryCell.self)
        collectionView.register(ShortcutCell.self)
        collectionView.register(MessageCell.self)

        collectionView.register(WatchlistTypeCell.self)
        collectionView.register(InstrumentCell.self)

        collectionView.register(BannerCell.self)
        collectionView.register(NewsLargeCell.self)
        collectionView.register(NewsCell.self)
        collectionView.register(FundRecommendationCell.self)
        collectionView.register(MeritReportCell.self)

        collectionView.register(
            WatchlistHeaderView.self,
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
            withReuseIdentifier: WatchlistHeaderView.reuseIdentifier)
        collectionView.register(
            TitleHeaderView.self,
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
            withReuseIdentifier: TitleHeaderView.reuseIdentifier)
        collectionView.register(
            FundRecommendationTitleHeaderView.self,
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
            withReuseIdentifier: FundRecommendationTitleHeaderView.reuseIdentifier)
        collectionView.register(
            MeritReportTitleHeaderView.self,
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
            withReuseIdentifier: MeritReportTitleHeaderView.reuseIdentifier)
        collectionView.register(
            SeeMoreFooterView.self,
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionFooter,
            withReuseIdentifier: SeeMoreFooterView.reuseIdentifier)
        collectionView.register(
            IndexIndicatorFooterView.self,
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionFooter,
            withReuseIdentifier: IndexIndicatorFooterView.reuseIdentifier)

        collectionView.dataSource = self
        collectionView.delegate = self

        collectionView.contentInset = UIEdgeInsets(with: 16)
        collectionView.refreshControl = refreshControl

        return collectionView
    }()

    var bannerIndicator: StepIndicatorView?

    // MARK: Properties
    var viewModel: HomeViewModel!
    private let onReloadData = PublishSubject<Void>()
    private let onOpenWatchListSorting = PublishSubject<Void>()

    // Datas
    private var walletSummaryDM: WalletSummaryCell.DisplayModel?
    private var updateMessages: [MessageCell.DisplayModel] = []
    private var watchListSorting: WatchListSortingType?
    // Preserve fund recommendation category selection across loading and navigation
    private var selectedFundCategoryIndex: Int = 0

    // MARK: Life cycle
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        bindActions()
        bindViewModelLogic()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        searchBar.dropShadow(
            alpha: 1,
            opacity: currentAppTheme == .light ? 0.07 : 0.5,
            offset: .init(width: 0, height: 6),
            radius: 4)
    }

    override func setTexts() {
        searchBar.setPlaceholder("key0400".localized())
        collectionView.reloadData()
    }

    func setupUI() {
        view.backgroundColor = Color.bgDefault

        view.addSubviews([
            collectionView,
            searchBar,
        ])

        searchBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.height.equalTo(60)
        }

        collectionView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide)
        }
    }

    func bindActions() {
        searchBar.onTapSearch
            .subscribe(onNext: { [weak self] in
                self?.viewModel.navigate(to: .search)
            })
            .disposed(by: disposeBag)

        searchBar.onTapProfile
            .subscribe(onNext: { [weak self] in
                self?.viewModel.navigate(to: .appSettings)
            })
            .disposed(by: disposeBag)

        searchBar.onTapNotification
            .subscribe(onNext: { [weak self] in
                self?.viewModel.navigate(to: .notification)
            })
            .disposed(by: disposeBag)

        refreshControl.rx.controlEvent(.valueChanged)
            .subscribe(onNext: { [unowned self] in
                self.onReloadData.onNext(())
            })
            .disposed(by: disposeBag)
    }

    func bindViewModelLogic() {
        let input = HomeViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(),
            reloadData: onReloadData.asObservable(),
            onOpenWatchListSorting: onOpenWatchListSorting.asObservable())

        let output = viewModel.transform(input: input)

        output.error
            .drive(onNext: { [unowned self] error in
                refreshControl.endRefreshing()
            })
            .disposed(by: disposeBag)

        output.loading
            .drive(onNext: { [unowned self] loading in
                if !loading {
                    refreshControl.endRefreshing()
                }
                searchBar.showLoadingBar(loading)
            })
            .disposed(by: disposeBag)

        output.updateWalletSummary
            .drive(onNext: { [unowned self] walletSummary in
                walletSummaryDM = walletSummary
                collectionView.reloadData()
            })
            .disposed(by: disposeBag)

        output.displayWatchListSorting
            .drive(onNext: { [unowned self] sortingData in
                watchListSorting = sortingData
                collectionView.reloadData()
            }).disposed(by: disposeBag)

        output.updateWatchlist
            .drive(onNext: { [unowned self] in
                collectionView.reloadData()
            })
            .disposed(by: disposeBag)

        output.updateBanner
            .drive(onNext: { [unowned self] in
                if bannerIndicator != nil {
                    bannerIndicator?.removeFromSuperview()
                    bannerIndicator = nil
                }
                if !viewModel.banners.isEmpty {
                    bannerIndicator = StepIndicatorView(totalSteps: viewModel.banners.count)

                    collectionView.addSubview(bannerIndicator!)
                    bannerIndicator!.snp.makeConstraints { make in
                        make.top.equalTo(105)
                        make.centerX.equalToSuperview()
                    }
                }

                collectionView.reloadData()
            })
            .disposed(by: disposeBag)

        output.updateNews
            .drive(onNext: { [unowned self] in
                collectionView.reloadData()
            })
            .disposed(by: disposeBag)

        output.updateFundRecommendations
            .drive(onNext: { [unowned self] in
                validateSelectedFundCategoryIndex()
                collectionView.reloadData()
            })
            .disposed(by: disposeBag)

        output.updateMeritReports
            .drive(onNext: { [unowned self] in
                collectionView.reloadData()
            })
            .disposed(by: disposeBag)
    }

    private func showActionBlockBottomSheet(accountStatus: AccountStatus) {
        let bottomSheet = AccountBlockActionBottomSheet(accountStatus: accountStatus)
        bottomSheet.rx.tapAction
            .subscribe(onNext: { [unowned self] accountStatus in
                switch accountStatus {
                case .onboarding,
                    .pendingReview:
                    viewModel.navigate(to: .onboarding(.current))

                default:
                    return
                }
            }).disposed(by: disposeBag)

        bottomSheet.present()
    }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegate
extension HomeViewController: UICollectionViewDataSource, UICollectionViewDelegate {

    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return viewModel.homeSections.count
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
        -> Int
    {
        switch viewModel.homeSections[section] {
        case .userProfile:
            return 1

        case .shortcut:
            return viewModel.shortcutItems().count

        case .message:
            return updateMessages.count

        case .watchList:
            return 1

        case .banner:
            return viewModel.banners.count

        case .news:
            return viewModel.getNewsList().count

        case .fundRecommendation:
            let fundData = viewModel.getFundRecommendations()
            let count = fundData != nil ? 1 : 0
            print("🏦 [FundRec] numberOfItemsInSection: \(count), data exists: \(fundData != nil)")
            return count

        case .meritReport:
            return viewModel.getMeritReportsList().count

        default:
            return 0
        }
    }

    func collectionView(
        _ collectionView: UICollectionView,
        cellForItemAt indexPath: IndexPath
    ) -> UICollectionViewCell {
        switch viewModel.homeSections[indexPath.section] {
        case .userProfile:
            let cell: WalletSummaryCell = collectionView.dequeueReusableCell(
                forIndexPath: indexPath)
            if let walletSummary = self.walletSummaryDM {
                cell.updateUI(with: walletSummary)
            }

            return cell

        case .shortcut:
            let cell: ShortcutCell = collectionView.dequeueReusableCell(forIndexPath: indexPath)
            let shortcut = viewModel.shortcutItems()[indexPath.item]

            cell.update(shortcut: shortcut)

            return cell

        case .message:
            let cell: MessageCell = collectionView.dequeueReusableCell(forIndexPath: indexPath)
            cell.updateUI(with: updateMessages[indexPath.row])

            return cell

        case .watchList:
            let cell: InstrumentCell = collectionView.dequeueReusableCell(forIndexPath: indexPath)

            cell.update(assets: viewModel.watchlistItems)

            cell.rx.selectAsset
                .subscribe(onNext: { [unowned self] asset in
                    let instrument = MarketInstrument(
                        id: asset.instrumentId,
                        symbol: asset.symbol,
                        exchange: asset.exchange,
                        instrumentName: asset.name,
                        currency: asset.currency,
                        logo: asset.logo,
                        riskLevel: asset.riskLevel)

                    viewModel.navigate(to: .instrumentDetail(instrument: instrument))
                })
                .disposed(by: cell.disposeBag)

            return cell

        case .banner:
            let cell: BannerCell = collectionView.dequeueReusableCell(forIndexPath: indexPath)
            cell.setBanner(viewModel.banners[indexPath.row])

            return cell

        case .news:
            let news = viewModel.getNewsList()[indexPath.item]

            if indexPath.item == 0 {
                let cell: NewsLargeCell = collectionView.dequeueReusableCell(
                    forIndexPath: indexPath)
                cell.updateUI(with: news)

                return cell
            }

            let cell: NewsCell = collectionView.dequeueReusableCell(forIndexPath: indexPath)
            cell.updateUI(with: news)

            return cell

        case .fundRecommendation:
            let cell: FundRecommendationCell = collectionView.dequeueReusableCell(
                forIndexPath: indexPath)
            if let fundRecommendations = viewModel.getFundRecommendations() {
                // Configure cell with preserved category selection
                cell.configure(with: fundRecommendations, selectedCategoryIndex: selectedFundCategoryIndex)

                // Track category selection for More button
                cell.categorySelection
                    .subscribe(onNext: { [weak self] selectedIndex in
                        self?.selectedFundCategoryIndex = selectedIndex
                    })
                    .disposed(by: cell.disposeBag)

                // Handle instrument tap navigation
                cell.instrumentTap
                    .subscribe(onNext: { [weak self] instrument in
                        self?.navigateToInstrumentDetail(instrument)
                    })
                    .disposed(by: cell.disposeBag)
            }
            return cell

        case .meritReport:
            let cell: MeritReportCell = collectionView.dequeueReusableCell(forIndexPath: indexPath)
            let meritReport = viewModel.getMeritReportsList()[indexPath.item]
            cell.updateUI(with: meritReport)

            return cell

        default:
            return UICollectionViewCell()
        }
    }

    func collectionView(
        _ collectionView: UICollectionView,
        viewForSupplementaryElementOfKind kind: String,
        at indexPath: IndexPath
    ) -> UICollectionReusableView {
        switch kind {
        // Header
        case UICollectionView.elementKindSectionHeader:
            switch viewModel.homeSections[indexPath.section] {
            case .watchlistHeader:
                guard
                    let headerView = collectionView.dequeueReusableSupplementaryView(
                        ofKind: kind,
                        withReuseIdentifier: WatchlistHeaderView.reuseIdentifier,
                        for: indexPath) as? WatchlistHeaderView
                else { return WatchlistHeaderView() }

                headerView.set(sorting: watchListSorting)

                headerView.rx.onOpenSorting
                    .subscribe(onNext: { [unowned self] in
                        onOpenWatchListSorting.onNext(())
                    })
                    .disposed(by: headerView.disposeBag)

                return headerView

            case .newsHeader:
                guard
                    let headerView = collectionView.dequeueReusableSupplementaryView(
                        ofKind: kind,
                        withReuseIdentifier: TitleHeaderView.reuseIdentifier,
                        for: indexPath) as? TitleHeaderView
                else { return TitleHeaderView() }
                headerView.update(title: "NEWS")

                return headerView

            case .fundRecommendationHeader:
                guard
                    let headerView = collectionView.dequeueReusableSupplementaryView(
                        ofKind: kind,
                        withReuseIdentifier: FundRecommendationTitleHeaderView.reuseIdentifier,
                        for: indexPath) as? FundRecommendationTitleHeaderView
                else { return FundRecommendationTitleHeaderView() }

                // Use dynamic title from API response, fallback to localized string if no data
                let title =
                    viewModel.getFundRecommendations()?.categoryName ?? "key1064".localized()
                headerView.update(title: title)

                // Handle More button tap
                headerView.rx.tapMore
                    .subscribe(onNext: { [weak self] in
                        self?.handleMoreButtonTap()
                    })
                    .disposed(by: headerView.disposeBag)

                return headerView

            case .meritReportHeader:
                guard
                    let headerView = collectionView.dequeueReusableSupplementaryView(
                        ofKind: kind,
                        withReuseIdentifier: MeritReportTitleHeaderView.reuseIdentifier,
                        for: indexPath) as? MeritReportTitleHeaderView
                else { return MeritReportTitleHeaderView() }
                headerView.update(title: "key1065".localized())

                return headerView

            default:
                return UICollectionReusableView()
            }

        // Footer
        case UICollectionView.elementKindSectionFooter:
            guard
                let footerView = collectionView.dequeueReusableSupplementaryView(
                    ofKind: kind,
                    withReuseIdentifier: SeeMoreFooterView.reuseIdentifier,
                    for: indexPath) as? SeeMoreFooterView
            else { return SeeMoreFooterView() }

            switch self.viewModel.homeSections[indexPath.section] {
            case .message:
                footerView.showButton(!updateMessages.isEmpty)

            default:
                footerView.showButton(true)
            }

            footerView.rx.tapSeeMore
                .subscribe(onNext: { [unowned self] in
                    switch self.viewModel.homeSections[indexPath.section] {
                    case .news:
                        self.viewModel.navigate(to: .newsList)
                    case .message:
                        self.viewModel.navigate(to: .notification)

                    default:
                        break
                    }
                })
                .disposed(by: footerView.disposeBag)

            return footerView

        default:
            assertionFailure("Unexpected element kind: \(kind).")
            return UICollectionReusableView()
        }
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)

        switch viewModel.homeSections[indexPath.section] {
        case .news:
            guard let url = viewModel.getNewsList()[indexPath.item].url else { return }

            viewModel.navigate(to: .newsDetail(url: url))

        case .message:
            viewModel.navigate(to: .notification)

        case .shortcut:
            let shortcut = viewModel.shortcutItems()[indexPath.item]

            switch shortcut {
            case .deposit,
                .withdraw:
                guard
                    let accountStatus = AccountStatus(
                        rawValue: Keychain.userInformation?.accountStatus ?? "")
                else { return }

                switch accountStatus {
                case .normal,
                    .approved:
                    viewModel.navigate(
                        to: .walletAction(action: shortcut == .deposit ? .deposit : .withdraw))

                default:
                    showActionBlockBottomSheet(accountStatus: accountStatus)
                }

            case .statement:
                viewModel.navigate(to: .statementRequest)

            case .favorites:
                viewModel.navigate(to: .favoriteList)
            }

        case .banner:
            guard
                let bannerTitle = viewModel.banners[indexPath.row].title,
                let targetUrl = viewModel.banners[indexPath.row].targetUrl
            else { return }

            viewModel.navigate(to: .webview(title: bannerTitle, urlString: targetUrl))

        case .meritReport:
            let meritReport = viewModel.getMeritReportsList()[indexPath.item]

            // Validate URL before navigation
            guard !meritReport.webUrl.isEmpty,
                URL(string: meritReport.webUrl) != nil
            else {
                // Handle invalid URL case - could show an alert or log error
                print("Invalid URL for Merit Report: \(meritReport.webUrl)")
                return
            }

            // Validate URL before navigation
            guard !meritReport.webUrl.isEmpty,
                URL(string: meritReport.webUrl) != nil
            else {
                print("❌ [DEBUG] Invalid URL, cannot create URL object")
                return
            }

            // Use enhanced SimpleWebViewController with Wormholy detection
            print("🔍 [DEBUG] Using SimpleWebViewController with enhanced Wormholy detection")
            let webViewController = SimpleWebViewController(
                titleString: meritReport.title, urlString: meritReport.webUrl)
            webViewController.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(webViewController, animated: true)

        default:
            break
        }
    }

    // MARK: - Private Methods
    private func handleMoreButtonTap() {
        guard let fundRecommendations = viewModel.getFundRecommendations() else { return }

        // Get the currently selected category from the tracked index
        guard selectedFundCategoryIndex < fundRecommendations.children.count else { return }
        let selectedCategory = fundRecommendations.children[selectedFundCategoryIndex]

        viewModel.navigate(
            to: .fundRecommendationList(
                category: selectedCategory,
                mainCategoryName: fundRecommendations.categoryName
            ))
    }

    private func navigateToInstrumentDetail(_ fundInstrument: FundRecommendationInstrument) {
        let marketInstrument = viewModel.convertToMarketInstrument(from: fundInstrument)
        viewModel.navigate(to: .instrumentDetail(instrument: marketInstrument))
    }

    // MARK: - Fund Recommendation State Management

    /// Validates and adjusts the selected fund category index to ensure it's within valid bounds
    /// This is called when fund recommendation data is updated to prevent index out of bounds
    private func validateSelectedFundCategoryIndex() {
        guard let fundRecommendations = viewModel.getFundRecommendations() else {
            selectedFundCategoryIndex = 0
            return
        }

        // Ensure the selected index is within the valid range
        let maxIndex = max(0, fundRecommendations.children.count - 1)
        selectedFundCategoryIndex = min(selectedFundCategoryIndex, maxIndex)
    }
}
// swiftlint:enable line_length
