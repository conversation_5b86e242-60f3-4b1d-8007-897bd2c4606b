//
//  HomeViewModel.swift
//  Home
//
//  Created by <PERSON> on 19/02/2024.
//

import APILayer
import CUIModule
import Core
import Networking
import RxCocoa
import RxSwift
import SharedData
import Storage
import XCoordinator

// MARK: - Fund Recommendation Models
public struct FundRecommendationCategory {
    public let categoryId: Int
    public let categoryName: String
    public let children: [FundRecommendationChild]
    public let instruments: [FundRecommendationInstrument]

    public init(
        categoryId: Int, categoryName: String, children: [FundRecommendationChild],
        instruments: [FundRecommendationInstrument]
    ) {
        self.categoryId = categoryId
        self.categoryName = categoryName
        self.children = children
        self.instruments = instruments
    }
}

public struct FundRecommendationChild {
    public let categoryId: Int
    public let categoryName: String
    public let instruments: [FundRecommendationInstrument]

    public init(categoryId: Int, categoryName: String, instruments: [FundRecommendationInstrument])
    {
        self.categoryId = categoryId
        self.categoryName = categoryName
        self.instruments = instruments
    }
}

public struct FundRecommendationInstrument {
    public let instrumentId: Int
    public let instrumentName: String
    public let symbol: String
    public let exchange: String
    public let currency: String
    public let logo: String?
    public let price: String?
    public let priceChange: String?
    public let priceChangeRate: String?
    public let allocationClass: String?
    public let riskLevel: String?
    public let period: String?

    public init(
        instrumentId: Int, instrumentName: String, symbol: String, exchange: String,
        currency: String, logo: String?, price: String?, priceChange: String?,
        priceChangeRate: String?, allocationClass: String?, riskLevel: String?, period: String?
    ) {
        self.instrumentId = instrumentId
        self.instrumentName = instrumentName
        self.symbol = symbol
        self.exchange = exchange
        self.currency = currency
        self.logo = logo
        self.price = price
        self.priceChange = priceChange
        self.priceChangeRate = priceChangeRate
        self.allocationClass = allocationClass
        self.riskLevel = riskLevel
        self.period = period
    }
}

// swiftlint:disable file_length
final class HomeViewModel: AnyViewModel {

    struct Input {
        let onViewAppear: Observable<Void>
        let reloadData: Observable<Void>
        let onOpenWatchListSorting: Observable<Void>
    }

    struct Output {
        let loading: Driver<Bool>
        let error: Driver<Error>
        let updateWalletSummary: Driver<WalletSummaryCell.DisplayModel>
        let displayWatchListSorting: Driver<WatchListSortingType>
        let updateWatchlist: Driver<Void>
        let updateBanner: Driver<Void>
        let updateNews: Driver<Void>
        let updateMessage: Driver<[MessageCell.DisplayModel]>
        let currencies: Driver<[CurrencyView.DisplayModel]>
        let updateFundRecommendations: Driver<Void>
        let updateMeritReports: Driver<Void>
    }

    // MARK: Properties
    private let disposeBag = DisposeBag()
    private let router: UnownedRouter<HomeRoute>
    private(set) var homeSections: [HomeSection]

    private let watchListSorting = BehaviorSubject<WatchListSortingType>(value: .gainer)
    private(set) var watchlistItems: [WatchListAsset] = []

    private(set) var banners: [Banner] = []
    private(set) var meritReports: [MeritReport] = []
    private(set) var fundRecommendations: FundRecommendationCategory?

    init(router: UnownedRouter<HomeRoute>) {
        self.router = router

        homeSections = [
            .banner,
            .shortcut,
            .userProfile,
            .fundRecommendationHeader,
            .fundRecommendation,
            .meritReportHeader,
            .meritReport,
        ]
    }

    // swiftlint:disable:next function_body_length
    func transform(input: Input) -> Output {
        let activityIndicator = ActivityIndicator()
        let errorTracker = ErrorTracker()

        let onLoadData = Observable.merge(
            input.onViewAppear,
            input.reloadData)

        let onLoadWatchlist = Observable.merge(
            watchListSorting.skip(1),
            onLoadData.withLatestFrom(watchListSorting))

        let onLoadMessage = Observable.merge(
            input.onViewAppear,
            input.reloadData)

        let walletSummary =
            onLoadData
            .flatMap {
                EnumerationEndPoint.service.fetchAllocationClasses()
                    .track(activityIndicator, error: errorTracker)
            }
            .flatMap { [unowned self] in
                queryWalletSummary()
                    .track(activityIndicator, error: errorTracker)
            }
            .map { self.mapWalletSummary(from: $0) }

        input.onOpenWatchListSorting
            .withLatestFrom(watchListSorting)
            .subscribe(onNext: { [unowned self] in
                navigate(
                    to: .watchListSorting(
                        sorting: $0,
                        onApply: watchListSorting.asObserver()))
            }).disposed(by: disposeBag)

        let updateWatchlist =
            onLoadWatchlist
            .flatMap { sorting in
                MarketWatchListEndPoint.service.call(with: sorting)
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] value in
                watchlistItems = value
            }
            .mapToVoid()

        let getBanners =
            onLoadData
            .flatMap {
                ContentBannerListEndPoint.service.call()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] value in
                banners = value.sorted(by: { $0.sort ?? 0 < $1.sort ?? 0 })
            }
            .mapToVoid()

        let getMeritReports =
            onLoadData
            .flatMap { [unowned self] in
                queryMeritReports()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] value in
                meritReports = value
            }
            .mapToVoid()

        let getFundRecommendations =
            onLoadData
            .flatMap { [unowned self] in
                queryFundRecommendations()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { [unowned self] value in
                fundRecommendations = value
            }
            .mapToVoid()

        let getNews =
            onLoadData
            .filter {
                (LocalPreference.lastTimeGettingNews ?? "")
                    != DateFormatHelper.stringFrom(
                        date: Date(),
                        format: "yyyy-MM-dd")
            }
            .flatMap { [unowned self] in
                queryNews()
                    .track(activityIndicator, error: errorTracker)
            }
            .onNext { news in
                LocalPreference.saveNews(news)
                LocalPreference.lastTimeGettingNews = DateFormatHelper.stringFrom(
                    date: Date(),
                    format: "yyyy-MM-dd")
            }
            .mapToVoid()

        let getCachedNews =
            onLoadData
            .filter {
                (LocalPreference.lastTimeGettingNews ?? "")
                    == DateFormatHelper.stringFrom(
                        date: Date(),
                        format: "yyyy-MM-dd")
            }
            .mapToVoid()

        let updateNews = Observable.merge(
            getNews,
            getCachedNews)

        let messageList =
            onLoadMessage
            .flatMap { [unowned self] in
                fetchMessageList()
                    .track(activityIndicator, error: errorTracker)
            }
            .share()

        let updateMessage =
            messageList
            .map { [unowned self] in
                mapToMessageListDisplayModel(from: $0)
            }

        let currencies = input.onViewAppear
            .flatMap { [unowned self] in
                queryCurrencies()
                    .track(activityIndicator, error: errorTracker)
            }
            .share()

        let currencyDisplayData =
            currencies
            .map(mapToCurrencies)

        return Output(
            loading: activityIndicator.asDriver(),
            error: errorTracker.asDriver(),
            updateWalletSummary: walletSummary.asDriverOnErrorNever(),
            displayWatchListSorting: watchListSorting.asDriverOnErrorNever(),
            updateWatchlist: updateWatchlist.asDriverOnErrorNever(),
            updateBanner: getBanners.asDriverOnErrorNever(),
            updateNews: updateNews.asDriverOnErrorNever(),
            updateMessage: updateMessage.asDriverOnErrorNever(),
            currencies: currencyDisplayData.asDriverOnErrorNever(),
            updateFundRecommendations: getFundRecommendations.asDriverOnErrorNever(),
            updateMeritReports: getMeritReports.asDriverOnErrorNever())
    }

    func navigate(to route: HomeRoute) {
        router.trigger(route)
    }
}

// MARK: - API
extension HomeViewModel {

    fileprivate func queryMarketList(for exchange: MarketExchange) -> Observable<
        MarketListDetailEndpoint.Response
    > {
        let request = MarketListDetailEndpoint.Request(listCode: "BENCHMARK")

        return MarketListDetailEndpoint.service.request(parameters: request)
    }

    fileprivate func queryQuoteInfo(for instrumentId: Int) -> Observable<
        MarketQuoteActivityEndPoint.Response
    > {
        let request = MarketQuoteActivityEndPoint.Request(instrumentId: instrumentId)
        return MarketQuoteActivityEndPoint.service.request(parameters: request)
    }

    fileprivate func queryWalletSummary() -> Observable<WalletSummaryEndpoint.Response> {
        WalletSummaryEndpoint.service.call(with: .allocationClass)
    }

    fileprivate func queryPortfolios(for exchange: MarketExchange) -> Observable<
        [WalletSummaryEndpoint.Asset]
    > {
        WalletSummaryEndpoint.service.call(with: .allocationClass)
            .map { response in
                guard let portfolioList = response.summaryList?.first?.assetList else { return [] }

                return portfolioList.filter { $0.exchange == exchange.queryValue }
            }
    }

    fileprivate func queryFavorite(for exchange: MarketExchange) -> Observable<[MarketInstrument]> {
        return MarketGetFavoriteEndPoint.service.call()
            .map { response in
                guard let instruments = response.instruments else { return [] }
                return Array(instruments.filter({ $0.exchange == exchange.queryValue }).prefix(5))
            }
    }

    fileprivate func queryRecomenedList(for exchange: MarketExchange) -> Observable<
        [MarketInstrument]
    > {
        let request = MarketListDetailEndpoint.Request(listCode: "BENCHMARK")

        return MarketListDetailEndpoint.service.request(parameters: request)
            .map { response in
                return Array(
                    response.instruments.filter { $0.exchange == exchange.queryValue }.prefix(5))
            }
    }

    fileprivate func queryNews() -> Observable<[News]> {
        let request = GetMarketNewsEndPoint.Request(
            publishedOn: DateFormatHelper.stringFrom(
                date: Date(),
                format: "yyyy-MM-dd"))
        return GetMarketNewsEndPoint.service.call(parameter: request)
            .map { $0.data }
    }

    fileprivate func queryActiveAutoOrders() -> Observable<Int> {
        ListAutoOrderEndPoint.service.call().map {
            $0.autoOrderItem?.filter {
                $0.autoOrderBase?.tradingPlanStatus == AutoOrderState.active.queryValue
            }
            .count ?? 0
        }
    }

    fileprivate func queryOfflineOrders() -> Observable<Int> {
        let request = OfflineOrderListEndPoint.Request(
            pageSize: 100,
            start: 0,
            end: 0,
            orderTimeSortBy: "NEWEST")

        return OfflineOrderListEndPoint.service.request(parameters: request)
            .map {
                $0.list?.filter { $0.status == "QUEUED" }
                    .count ?? 0
            }
    }

    fileprivate func fetchMessageList() -> Observable<UserNotificationEndPoint.Response> {
        UserNotificationEndPoint.service.call(
            with: 3,
            messageType: .notification)
    }

    fileprivate func queryCurrencies() -> Observable<CurrencyEndpoint.Response> {
        CurrencyEndpoint.service.call()
    }

    fileprivate func queryFundRecommendations() -> Observable<FundRecommendationCategory?> {
        // Call the real API endpoint
        return MarketProductCategoryListEndPoint.service.call()
            .map { [weak self] (response: MarketProductCategoryListEndPoint.Response) in
                // Take the first category as the main category (as per requirements)
                guard let firstCategory = response.list.first else {
                    print("🏦 [DEBUG] No product categories returned from API")
                    return nil
                }

                print(
                    "🏦 [DEBUG] API returned \(response.list.count) categories, using first: \(firstCategory.categoryName)"
                )

                // Map API response to UI model
                return self?.mapToFundRecommendationCategory(from: firstCategory)
            }
            .catch { error in
                print("🏦 [ERROR] Failed to fetch fund recommendations: \(error)")
                return Observable.just(nil)
            }
    }

    private func mapToFundRecommendationCategory(
        from productCategory: MarketProductCategoryListEndPoint.ProductCategory
    ) -> FundRecommendationCategory {
        // Map children categories
        let children = productCategory.children.map { child in
            FundRecommendationChild(
                categoryId: child.id,
                categoryName: child.categoryName,
                instruments: child.instruments.map { instrument in
                    FundRecommendationInstrument(
                        instrumentId: instrument.id,
                        instrumentName: instrument.instrumentName,
                        symbol: instrument.symbol,
                        exchange: "",  // Not provided in API response
                        currency: "",  // Not provided in API response
                        logo: nil,  // Not provided in API response
                        price: nil,  // Not provided in API response
                        priceChange: nil,  // Not provided in API response
                        priceChangeRate: instrument.priceChangeRate,
                        allocationClass: instrument.allocationClass,
                        riskLevel: instrument.riskLevel,
                        period: instrument.period
                    )
                }
            )
        }

        // Map main category - create empty instruments array since it's not in the API response
        return FundRecommendationCategory(
            categoryId: productCategory.id,
            categoryName: productCategory.categoryName,
            children: children,
            instruments: []  // Not provided in API response for main category
        )
    }

    fileprivate func queryMeritReports() -> Observable<[MeritReport]> {
        // Create a custom endpoint for Merit Reports
        struct MeritReportEndpoint: NetworkEndpoint {
            var path: String = "/content/article/list"
            var method: RequestMethod = .post
            var encoding: RequestEncoding = .json
            var headers: RequestHeaders?

            struct Request: Codable {
                let pageNo: Int
                let pageSize: Int
            }

            struct Response: Codable {
                let total: Int
                let list: [MeritReportItem]
            }

            struct MeritReportItem: Codable {
                let id: Int
                let uuid: String
                let title: String
                let status: Int
                let webUrl: String
                let banner: Int
                let created: Int
                let published: Int64
            }
        }

        var endpoint = MeritReportEndpoint()
        let request = MeritReportEndpoint.Request(pageNo: 1, pageSize: 10)

        return endpoint.request(parameters: request)
            .map { (response: MeritReportEndpoint.Response) in
                return response.list.enumerated().map { (index, item) in
                    let meritReport = MeritReport(
                        id: item.id,
                        uuid: item.uuid,
                        title: item.title,
                        status: item.status,
                        webUrl: item.webUrl,
                        banner: item.banner,
                        created: item.created,
                        published: item.published
                    )
                    return meritReport
                }
            }
    }
}

// MARK: - Private
extension HomeViewModel {

    fileprivate func mapMarketStatus(
        for exchange: MarketExchange,
        quote: MarketQuoteActivityEndPoint.Response
    ) -> InstrumentStatusView.DisplayModel {

        let country = exchange == .nasdaq ? "United States" : "Thailand"
        let shortCountry = exchange == .nasdaq ? "US" : "TH"
        let exchangeTimeZone = exchange == .nasdaq ? "America/New_York" : "Asia/Bangkok"

        return InstrumentStatusView.DisplayModel(
            status: quote.marketStatus ?? "",
            lastMatchedTime: Date().milliseconds,
            country: country,
            shortCountry: shortCountry,
            countryLogo: "",
            exchangeTimeZone: exchangeTimeZone)
    }

    fileprivate func mapWalletSummary(from response: WalletSummaryEndpoint.Response)
        -> WalletSummaryCell.DisplayModel
    {
        let distributionItems = response.sortedSummaries().enumerated().map { index, summary in
            let allocationClass = InstrumentInvestmenentModels.allocationClasses.first(where: {
                $0.queryValue == summary.summaryName
            })
            return DistributionBarView.DistributionItemModel(
                name: allocationClass?.title ?? summary.summaryName ?? "",
                value: summary.percentage?.toNumber() ?? 0,
                color: OverviewPieChartView.chartColor(
                    for: summary.summaryName,
                    at: index))
        }

        return WalletSummaryCell.DisplayModel(
            totalBalance: response.totalValue,
            unrelalizedGLRate: response.unrealizedGlRate,
            unrelalizedGLValue: response.unrealizedGl,
            currency: response.currency,
            distributionData: .init(items: distributionItems))
    }

    fileprivate func mapMostActiveInstrument(from instruments: [MarketInstrument])
        -> [MarketInstrument]
    {
        let mostActives = Array(
            instruments.sorted {
                $0.totalAmount.toNumberOrNil() ?? 0 > $1.totalAmount.toNumberOrNil() ?? 0
            }
            .prefix(5))

        if StoredData.mostActiveSETInstrument == nil {
            StoredData.mostActiveSETInstrument = mostActives.first
        }

        return mostActives
    }

    fileprivate func mapTopPerformingInstruments(from portfolios: [WalletSummaryEndpoint.Asset])
        -> [MarketInstrument]
    {
        let portfoliosList = portfolios
        //        Array(portfolios.sorted(by: {
        //            $0.unrealizedGLRate?.toNumber() ?? 0 > $1.unrealizedGLRate?.toNumber() ?? 0
        //        }).prefix(5))

        return portfoliosList.map {
            MarketInstrument(
                symbol: $0.symbol,
                exchange: $0.exchange,
                market: "",
                instrumentName: "",
                currency: $0.currency,
                logo: $0.logo,
                lastPrice: $0.marketValue)
        }
    }

    fileprivate func mapToMessageListDisplayModel(from response: UserNotificationEndPoint.Response)
        -> [MessageCell.DisplayModel]
    {
        var messageDatas: [MessageCell.DisplayModel] = []

        let messageList = response.list ?? []

        messageDatas = messageList.map { message in
            let date = Date(timeIntervalSince1970: TimeInterval((message.datetime ?? 0) / 1000))
            let businessType = NotificationBusinessType(rawValue: message.businessType ?? "")

            return MessageCell.DisplayModel(
                logo: businessType?.logo,
                title: message.title ?? "",
                description: message.content ?? "",
                timeDiff: date.simpleFormat)
        }

        if messageDatas.isEmpty {
            homeSections.removeAll(where: { $0 == .message })
        } else if !homeSections.contains(.message) {
            homeSections.append(.message)
            homeSections = homeSections.sorted(by: { $0.rawValue < $1.rawValue })
        }

        //        LocalPreference.unreadMessageCount = unreadMessageCount
        NotificationManager.shared.checkUnreadNotification()
        return messageDatas
    }

    fileprivate func mapToCurrencies(_ response: CurrencyEndpoint.Response) -> [CurrencyView
        .DisplayModel]
    {
        response.list?.map {
            CurrencyView.DisplayModel(
                title: $0.exchange ?? "",
                amount: $0.exchangeRate?.toNumber() ?? 0,
                quality: .still)
        } ?? []
    }

    fileprivate func mapToExchangeRates(_ response: CurrencyEndpoint.Response) -> [ExchangeRateCell
        .DisplayModel]
    {
        response.list?.map {
            ExchangeRateCell.DisplayModel(
                title: $0.exchange ?? "",
                exchangeRate: $0.exchangeRate?.toNumber().formatted() ?? "")
        } ?? []
    }
}

// MARK: - Internal
extension HomeViewModel {

    func getNewsList() -> [News] {
        Array(LocalPreference.news?.prefix(6) ?? [])
    }

    func getMeritReportsList() -> [MeritReport] {
        return meritReports
    }

    func getFundRecommendations() -> FundRecommendationCategory? {
        return fundRecommendations
    }

    func shortcutItems() -> [HomeShortcutItem] {
        HomeShortcutItem.allCases
    }

    // MARK: - Utility Methods

    /// Converts FundRecommendationInstrument to MarketInstrument for navigation
    func convertToMarketInstrument(from fundInstrument: FundRecommendationInstrument) -> MarketInstrument {
        return MarketInstrument(
            id: fundInstrument.instrumentId,
            symbol: fundInstrument.symbol,
            exchange: fundInstrument.exchange.isEmpty ? nil : fundInstrument.exchange,
            TimeZone: nil,
            market: nil,
            instrumentName: fundInstrument.instrumentName,
            currency: fundInstrument.currency.isEmpty ? nil : fundInstrument.currency,
            logo: fundInstrument.logo,
            riskLevel: fundInstrument.riskLevel,
            riskRating: nil,
            instrumentClass: nil,
            instrumentType: nil,
            instrumentCategory: nil,
            allocation: fundInstrument.allocationClass,
            assetClass: nil,
            lastPrice: fundInstrument.price,
            priceChange: fundInstrument.priceChange,
            priceChangePercentage: fundInstrument.priceChangeRate,
            favorite: nil,
            totalVolume: nil,
            totalAmount: nil
        )
    }
}

// MARK: - extension MarketExchange
extension MarketExchange {

    var marketListParam: String {
        switch self {
        case .set:
            return "THAI MARKET"
        case .nasdaq:
            return "GLOBAL MARKET"
        }
    }
}

// MARK: - NotificationBusinessType
extension NotificationBusinessType {

    var logo: UIImage {
        switch self {
        case .order:
            return .image(named: "merit_ic_asset_placeholder")
        case .withdraw:
            return .image(named: "ic_noti_withdraw")
        case .deposit:
            return .image(named: "ic_noti_deposit")
        }
    }
}
// swiftlint:enable file_length
